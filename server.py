import os
import json
import logging
import tempfile
import requests
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import whisper

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class AudioTranscriptionHandler(BaseHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        # Carrega o modelo Whisper uma vez na inicialização
        if not hasattr(AudioTranscriptionHandler, 'whisper_model'):
            logger.info("Carregando modelo Whisper...")
            AudioTranscriptionHandler.whisper_model = whisper.load_model("base")
            logger.info("Modelo Whisper carregado com sucesso!")
        super().__init__(*args, **kwargs)

    def do_POST(self):
        """Processa requisições POST"""
        try:
            # Parse da URL para extrair query parameters
            parsed_url = urlparse(self.path)
            query_params = parse_qs(parsed_url.query)

            # Verifica se o parâmetro audio_url existe e não está vazio
            audio_url = query_params.get('audio_url', [None])[0]

            if not audio_url:
                self.send_error_response(400, "Parâmetro 'audio_url' é obrigatório")
                return

            logger.info(f"Processando áudio da URL: {audio_url}")

            # Baixa o áudio
            audio_file_path = self.download_audio(audio_url)
            if not audio_file_path:
                self.send_error_response(500, "Erro ao baixar o áudio")
                return

            try:
                # Transcreve o áudio
                transcription = self.transcribe_audio(audio_file_path)

                # Retorna a resposta em JSON
                response_data = {"response": transcription}
                self.send_json_response(200, response_data)

            finally:
                # Remove o arquivo temporário
                if os.path.exists(audio_file_path):
                    os.remove(audio_file_path)
                    logger.info(f"Arquivo temporário removido: {audio_file_path}")

        except Exception as e:
            logger.error(f"Erro no processamento da requisição: {e}", exc_info=True)
            self.send_error_response(500, "Erro interno do servidor")

    def download_audio(self, audio_url):
        """Baixa o áudio da URL fornecida"""
        try:
            # Faz o download do áudio
            response = requests.get(audio_url, timeout=30)
            response.raise_for_status()

            # Cria um arquivo temporário
            with tempfile.NamedTemporaryFile(delete=False, suffix='.audio') as temp_file:
                temp_file.write(response.content)
                temp_file_path = temp_file.name

            logger.info(f"Áudio baixado para: {temp_file_path}")
            return temp_file_path

        except requests.exceptions.RequestException as e:
            logger.error(f"Erro ao baixar áudio: {e}")
            return None
        except Exception as e:
            logger.error(f"Erro inesperado ao baixar áudio: {e}")
            return None

    def transcribe_audio(self, audio_file_path):
        """Transcreve o áudio usando Whisper"""
        try:
            logger.info(f"Transcrevendo arquivo: {audio_file_path}")
            result = self.whisper_model.transcribe(audio_file_path)
            transcribed_text = result["text"].strip()
            logger.info(f"Texto transcrito: {transcribed_text}")
            return transcribed_text

        except Exception as e:
            logger.error(f"Erro na transcrição com Whisper: {e}", exc_info=True)
            return ""

    def send_json_response(self, status_code, data):
        """Envia resposta JSON"""
        self.send_response(status_code)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')  # CORS
        self.end_headers()

        json_data = json.dumps(data, ensure_ascii=False)
        self.wfile.write(json_data.encode('utf-8'))

    def send_error_response(self, status_code, message):
        """Envia resposta de erro em JSON"""
        error_data = {"error": message}
        self.send_json_response(status_code, error_data)

    def do_OPTIONS(self):
        """Lida com requisições OPTIONS para CORS"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

    def log_message(self, format, *args):
        """Override para usar o logger configurado"""
        logger.info(f"{self.address_string()} - {format % args}")

def run_server(port=8000):
    """Inicia o servidor HTTP"""
    server_address = ('', port)
    httpd = HTTPServer(server_address, AudioTranscriptionHandler)

    logger.info(f"Servidor iniciado na porta {port}")
    logger.info(f"Para transcrever áudio, faça uma requisição POST para:")
    logger.info(f"http://localhost:{port}/?audio_url=<URL_DO_AUDIO>")

    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        logger.info("Servidor interrompido pelo usuário")
        httpd.shutdown()

if __name__ == "__main__":
    import sys

    # Permite especificar a porta como argumento
    port = 8000
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            logger.error("Porta deve ser um número inteiro")
            sys.exit(1)

    run_server(port)